"""
Простой тест для проверки работы пагинатора уроков
"""

import asyncio
from aiogram.types import InlineKeyboardButton
from common.pagination import Paginator


async def test_paginator():
    """Тест создания и работы пагинатора"""
    
    # Создаем тестовые кнопки (имитируем уроки)
    test_lessons = []
    for i in range(25):  # 25 уроков для тестирования пагинации
        lesson = InlineKeyboardButton(
            text=f"Урок {i+1}: Тестовая тема {i+1}",
            callback_data=f"lesson_{i+1}"
        )
        test_lessons.append(lesson)
    
    # Создаем пагинатор
    paginator = Paginator(
        data=test_lessons,
        per_page=8,  # 8 уроков на страницу
        per_row=1,   # По одному уроку в ряду
        lang="ru"    # Русский язык
    )
    
    print(f"✅ Пагинатор создан успешно")
    print(f"📊 Всего уроков: {await paginator.get_count()}")
    print(f"📄 Уроков на страницу: {paginator.per_page}")
    print(f"📚 Всего страниц: {(await paginator.get_count() + paginator.per_page - 1) // paginator.per_page}")
    
    # Тестируем рендеринг первой страницы
    try:
        keyboard = await paginator.render_kb(page=1)
        print(f"✅ Первая страница отрендерена успешно")
        print(f"🔘 Кнопок в клавиатуре: {len(keyboard.inline_keyboard)}")
        
        # Показываем структуру клавиатуры
        for i, row in enumerate(keyboard.inline_keyboard):
            if i < 8:  # Показываем только кнопки уроков
                print(f"   Урок {i+1}: {row[0].text}")
            elif i == 8:  # Кнопки навигации
                nav_texts = [btn.text for btn in row]
                print(f"   Навигация: {' | '.join(nav_texts)}")
            else:  # Кнопки "Назад" и "Главное меню"
                btn_texts = [btn.text for btn in row]
                print(f"   Системные: {' | '.join(btn_texts)}")
                
    except Exception as e:
        print(f"❌ Ошибка при рендеринге: {e}")
        return False
    
    # Тестируем рендеринг второй страницы
    try:
        keyboard_page2 = await paginator.render_kb(page=2)
        print(f"✅ Вторая страница отрендерена успешно")
        
        # Показываем первые несколько уроков второй страницы
        for i, row in enumerate(keyboard_page2.inline_keyboard):
            if i < 3:  # Показываем первые 3 урока второй страницы
                print(f"   Урок стр.2: {row[0].text}")
            elif i == 8:  # Кнопки навигации
                nav_texts = [btn.text for btn in row]
                print(f"   Навигация стр.2: {' | '.join(nav_texts)}")
                break
                
    except Exception as e:
        print(f"❌ Ошибка при рендеринге второй страницы: {e}")
        return False
    
    print(f"🎉 Все тесты пройдены успешно!")
    return True


if __name__ == "__main__":
    print("🧪 Запуск тестов пагинатора...")
    result = asyncio.run(test_paginator())
    if result:
        print("✅ Пагинатор работает корректно!")
    else:
        print("❌ Обнаружены проблемы в работе пагинатора!")
