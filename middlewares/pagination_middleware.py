"""
Middleware для добавления дополнительных кнопок к пагинаторам
"""
from typing import Callable, Dict, Any, Awaitable
from aiogram import BaseMiddleware
from aiogram.types import CallbackQuery, InlineKeyboardMarkup
from common.keyboards import get_main_menu_back_button


class PaginationMiddleware(BaseMiddleware):
    """
    Middleware для автоматического добавления кнопок навигации к пагинаторам уроков
    """
    
    async def __call__(
        self,
        handler: Callable[[CallbackQuery, Dict[str, Any]], Awaitable[Any]],
        event: CallbackQuery,
        data: Dict[str, Any]
    ) -> Any:
        print(f"🔧 PaginationMiddleware: получен callback {event.data}")

        # Выполняем основной обработчик
        result = await handler(event, data)

        # Проверяем, что это callback пагинации
        if (event.data and
            event.data.startswith("paginator:") and
            event.message and
            event.message.reply_markup):

            print(f"🔧 PaginationMiddleware: обрабатываем пагинацию {event.data}")
            await self._add_navigation_buttons(event)
        else:
            print(f"🔧 PaginationMiddleware: пропускаем {event.data}")

        return result
    
    async def _add_navigation_buttons(self, callback: CallbackQuery):
        """Добавляет кнопки навигации к клавиатуре пагинатора"""
        try:
            keyboard = callback.message.reply_markup

            # Проверяем, что это пагинатор (есть кнопки навигации)
            if not self._is_paginator_keyboard(keyboard):
                return

            # УБИРАЕМ ПРОВЕРКУ - ВСЕГДА ДОБАВЛЯЕМ КНОПКИ!
            # Сначала удаляем старые кнопки навигации, если они есть
            keyboard.inline_keyboard = [
                row for row in keyboard.inline_keyboard
                if not any("🏠 Главное меню" in button.text or "⬅️ Назад" in button.text
                          for button in row)
            ]

            # Добавляем кнопки навигации
            main_menu_buttons = get_main_menu_back_button()
            keyboard.inline_keyboard.extend(main_menu_buttons)

            # Обновляем клавиатуру
            await callback.message.edit_reply_markup(reply_markup=keyboard)

        except Exception as e:
            # Игнорируем ошибки, чтобы не нарушить работу пагинатора
            print(f"⚠️ Ошибка в PaginationMiddleware: {e}")
    
    def _is_paginator_keyboard(self, keyboard: InlineKeyboardMarkup) -> bool:
        """Проверяет, является ли клавиатура пагинатором"""
        if not keyboard.inline_keyboard:
            return False
        
        # Ищем кнопки пагинации
        pagination_symbols = ["<<", "<", ">", ">>", "/"]
        for row in keyboard.inline_keyboard:
            for button in row:
                if any(symbol in button.text for symbol in pagination_symbols):
                    return True
        return False
    
# Убрали функцию _has_navigation_buttons - теперь всегда добавляем кнопки
