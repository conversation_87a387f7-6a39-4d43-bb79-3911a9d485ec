"""
Пример использования обновленного пагинатора с интеграцией в общую систему навигации
"""

from aiogram.types import InlineKeyboardButton, CallbackQuery
from aiogram.fsm.context import FSMContext
from .pagination import Paginator


async def create_lessons_paginator(lessons: list) -> Paginator:
    """
    Создает пагинатор для списка уроков
    
    Args:
        lessons: Список уроков из базы данных
        
    Returns:
        Paginator: Настроенный пагинатор
    """
    # Преобразуем уроки в кнопки
    buttons = []
    for lesson in lessons:
        button = InlineKeyboardButton(
            text=f"📝 {lesson.name}",
            callback_data=f"lesson_{lesson.id}"
        )
        buttons.append(button)
    
    # Создаем пагинатор с кнопками
    paginator = Paginator(
        data=buttons,
        per_page=5,  # 5 уроков на страницу
        per_row=1,   # По одному уроку в ряду
        on_select=handle_lesson_selection,  # Обработчик выбора урока
        lang="ru"    # Русский язык
    )
    
    return paginator


async def handle_lesson_selection(callback: CallbackQuery, lesson_callback_data: str):
    """
    Обработчик выбора урока из пагинатора
    
    Args:
        callback: Callback query от пользователя
        lesson_callback_data: Данные callback'а урока (например, "lesson_123")
    """
    lesson_id = lesson_callback_data.replace("lesson_", "")
    
    await callback.message.edit_text(
        text=f"Вы выбрали урок с ID: {lesson_id}",
        reply_markup=None
    )
    await callback.answer()


async def show_lessons_with_pagination(callback: CallbackQuery, state: FSMContext, lessons: list):
    """
    Показывает список уроков с пагинацией
    
    Args:
        callback: Callback query
        state: FSM контекст
        lessons: Список уроков
    """
    # Создаем пагинатор
    paginator = await create_lessons_paginator(lessons)
    
    # Регистрируем пагинатор в роутере (если еще не зарегистрирован)
    # Это нужно сделать один раз при запуске бота
    # paginator.register(router)
    
    # Показываем первую страницу
    keyboard = await paginator.render_kb(page=1)
    
    await callback.message.edit_text(
        text=f"📚 Список уроков (всего: {len(lessons)}):",
        reply_markup=keyboard
    )
    await callback.answer()


# Пример использования с ленивой загрузкой
async def create_lazy_lessons_paginator(subject_id: int, course_id: int) -> Paginator:
    """
    Создает пагинатор с ленивой загрузкой уроков
    """
    async def load_lessons_page(cur_page: int, per_page: int) -> list[InlineKeyboardButton]:
        """Загружает уроки для конкретной страницы"""
        from database.repositories.lesson_repository import LessonRepository
        
        offset = (cur_page - 1) * per_page
        lessons = await LessonRepository.get_paginated(
            subject_id=subject_id,
            course_id=course_id,
            limit=per_page,
            offset=offset
        )
        
        buttons = []
        for lesson in lessons:
            button = InlineKeyboardButton(
                text=f"📝 {lesson.name}",
                callback_data=f"lesson_{lesson.id}"
            )
            buttons.append(button)
        
        return buttons
    
    async def get_lessons_count() -> int:
        """Получает общее количество уроков"""
        from database.repositories.lesson_repository import LessonRepository
        return await LessonRepository.count_by_subject_and_course(subject_id, course_id)
    
    # Создаем пагинатор с ленивой загрузкой
    paginator = Paginator(
        lazy_data=load_lessons_page,
        lazy_count=get_lessons_count,
        per_page=5,
        per_row=1,
        on_select=handle_lesson_selection,
        lang="ru"
    )
    
    return paginator
