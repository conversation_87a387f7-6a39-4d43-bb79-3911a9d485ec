"""
Простой пагинатор, который работает с FSM и Redis
Не использует виджетную систему, только обычные callback'и
"""

from typing import List
from aiogram.types import InlineKeyboardButton, InlineKeyboardMarkup, CallbackQuery
from aiogram.utils.keyboard import InlineKeyboardBuilder
from aiogram.fsm.context import FSMContext
from common.keyboards import get_main_menu_back_button


async def create_lessons_pagination_kb(lessons: List, current_page: int = 1, per_page: int = 8) -> InlineKeyboardMarkup:
    """
    Создает клавиатуру с пагинацией для уроков
    
    Args:
        lessons: Список уроков из БД
        current_page: Текущая страница (начиная с 1)
        per_page: Количество уроков на страницу
        
    Returns:
        InlineKeyboardMarkup: Клавиатура с уроками и пагинацией
    """
    builder = InlineKeyboardBuilder()
    
    # Вычисляем индексы для текущей страницы
    total_lessons = len(lessons)
    total_pages = (total_lessons + per_page - 1) // per_page if total_lessons > 0 else 1
    
    # Проверяем границы страницы
    if current_page < 1:
        current_page = 1
    elif current_page > total_pages:
        current_page = total_pages
    
    start_idx = (current_page - 1) * per_page
    end_idx = start_idx + per_page
    page_lessons = lessons[start_idx:end_idx]
    
    # Добавляем кнопки уроков
    for lesson in page_lessons:
        builder.add(InlineKeyboardButton(
            text=lesson.name,
            callback_data=f"lesson_{lesson.id}"
        ))
    
    # Настраиваем по одному уроку в ряду
    builder.adjust(1)
    
    # Добавляем кнопки навигации, если страниц больше одной
    if total_pages > 1:
        nav_buttons = []
        
        # Кнопка "В начало"
        if current_page > 1:
            nav_buttons.append(InlineKeyboardButton(text="<<", callback_data=f"lessons_page_1"))
        else:
            nav_buttons.append(InlineKeyboardButton(text=" ", callback_data="lessons_page_ignore"))
        
        # Кнопка "Назад"
        if current_page > 1:
            nav_buttons.append(InlineKeyboardButton(text="<", callback_data=f"lessons_page_{current_page - 1}"))
        else:
            nav_buttons.append(InlineKeyboardButton(text=" ", callback_data="lessons_page_ignore"))
        
        # Информация о странице
        nav_buttons.append(InlineKeyboardButton(text=f"{current_page} / {total_pages}", callback_data="lessons_page_ignore"))
        
        # Кнопка "Вперед"
        if current_page < total_pages:
            nav_buttons.append(InlineKeyboardButton(text=">", callback_data=f"lessons_page_{current_page + 1}"))
        else:
            nav_buttons.append(InlineKeyboardButton(text=" ", callback_data="lessons_page_ignore"))
        
        # Кнопка "В конец"
        if current_page < total_pages:
            nav_buttons.append(InlineKeyboardButton(text=">>", callback_data=f"lessons_page_{total_pages}"))
        else:
            nav_buttons.append(InlineKeyboardButton(text=" ", callback_data="lessons_page_ignore"))
        
        builder.row(*nav_buttons)
    
    # Добавляем кнопки "Назад" и "Главное меню"
    nav_buttons = get_main_menu_back_button()
    for button_row in nav_buttons:
        builder.row(*button_row)
    
    return builder.as_markup()


async def handle_lessons_pagination(callback: CallbackQuery, state: FSMContext):
    """
    Обработчик пагинации уроков
    
    Args:
        callback: CallbackQuery с данными о странице
        state: FSM контекст
    """
    # Извлекаем номер страницы из callback_data
    if callback.data == "lessons_page_ignore":
        await callback.answer()
        return
    
    try:
        page = int(callback.data.replace("lessons_page_", ""))
    except ValueError:
        await callback.answer("❌ Ошибка навигации")
        return
    
    # Получаем данные из FSM
    user_data = await state.get_data()
    subject_id = user_data.get("paginator_subject_id") or user_data.get("subject_id")
    course_id = user_data.get("paginator_course_id") or user_data.get("course_id")
    course_name = user_data.get("paginator_course_name") or user_data.get("course_name", "")
    subject_name = user_data.get("paginator_subject_name") or user_data.get("subject_name", "")
    
    if not subject_id or not course_id:
        await callback.answer("❌ Данные потеряны")
        return
    
    # Получаем уроки из БД
    from database import LessonRepository
    lessons = await LessonRepository.get_by_subject_and_course(subject_id, course_id)
    
    # Сохраняем текущую страницу в FSM
    await state.update_data(current_lessons_page=page)
    
    # Создаем клавиатуру для указанной страницы
    keyboard = await create_lessons_pagination_kb(lessons, page)
    
    # Обновляем сообщение
    await callback.message.edit_text(
        f"📚 Курс: {course_name}\n"
        f"📖 Предмет: {subject_name}\n\n"
        f"Выбери урок, по которому хочешь пройти домашнее задание:\n"
        f"(Всего уроков: {len(lessons)})",
        reply_markup=keyboard
    )
    
    await callback.answer()
